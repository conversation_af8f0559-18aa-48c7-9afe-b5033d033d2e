{% extends 'base.html' %}

{% block title %}Welcome - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
/* Welcome page specific responsive styles */
.welcome-container {
    max-width: 100%;
    padding: 0.5rem;
}

.welcome-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    text-align: center;
    box-shadow: var(--box-shadow);
}

.welcome-header h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.welcome-header .badge {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
    margin: 0.25rem;
}

.user-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    border: none;
}

.user-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.user-info h4 {
    color: var(--text-color);
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.user-info p {
    color: var(--text-color);
    opacity: 0.7;
    margin-bottom: 0;
    font-size: 0.9rem;
}

.status-alert {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.status-alert i {
    font-size: 1.2rem;
    margin-right: 0.75rem;
}

/* Performance metrics card */
.metrics-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.metrics-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.metrics-header i {
    margin-right: 0.5rem;
}

.metrics-body {
    padding: 1rem;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 500;
    color: var(--text-color);
}

.metric-value {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.85rem;
}

.metric-success {
    background-color: var(--success-color);
    color: white;
}

.metric-primary {
    background-color: var(--primary-color);
    color: white;
}

.metric-info {
    background-color: var(--secondary-color);
    color: white;
}

.metric-secondary {
    background-color: var(--light-color);
    color: var(--dark-color);
}

/* Action buttons */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.75rem;
    margin: 1rem 0;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.action-btn span {
    font-size: 0.85rem;
    text-align: center;
}

.btn-primary-action {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-success-action {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
    color: white;
}

.btn-info-action {
    background: linear-gradient(135deg, var(--secondary-color), #3498db);
    color: white;
}

.btn-outline-action {
    background: var(--card-bg);
    color: var(--text-color);
    border: 2px solid var(--border-color);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    color: inherit;
}

/* AI Insights section */
.ai-insights-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    border-left: 4px solid var(--secondary-color);
}

.ai-insights-header {
    background: linear-gradient(135deg, var(--secondary-color), #3498db);
    color: white;
    padding: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.ai-insights-header i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.ai-insights-body {
    padding: 1rem;
}

.ai-insight-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: rgba(var(--secondary-color-rgb), 0.05);
    border-radius: 6px;
    border-left: 3px solid var(--secondary-color);
}

.ai-insight-item:last-child {
    margin-bottom: 0;
}

.ai-insight-icon {
    color: var(--secondary-color);
    margin-right: 0.75rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.ai-insight-content {
    flex: 1;
    line-height: 1.4;
    color: var(--text-color);
}

.ai-insight-content strong {
    color: var(--primary-color);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .welcome-container {
        padding: 0.25rem;
    }

    .welcome-header {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .welcome-header h1 {
        font-size: 1.25rem;
    }

    .user-card {
        padding: 1rem;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .action-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .action-btn {
        padding: 0.75rem 0.5rem;
    }

    .action-btn i {
        font-size: 1.25rem;
    }

    .action-btn span {
        font-size: 0.8rem;
    }

    .metrics-body,
    .ai-insights-body {
        padding: 0.75rem;
    }

    .metric-item {
        padding: 0.5rem 0;
    }

    .ai-insight-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 480px) {
    .action-buttons {
        grid-template-columns: 1fr;
    }

    .metric-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .metric-value {
        align-self: flex-end;
    }

    .welcome-header h1 {
        font-size: 1.1rem;
    }

    .user-avatar {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }

    .user-info h4 {
        font-size: 1rem;
    }

    .user-info p {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="welcome-container">
    <!-- Welcome Header -->
    <div class="welcome-header">
        <h1>
            <i class="fas fa-check-circle me-2"></i>Authentication Successful
        </h1>
        <div class="badge">Test (UAT) Environment</div>
        <div class="badge bg-success">Mobile-Responsive v2.0</div>
    </div>

    <!-- User Information Card -->
    <div class="user-card">
        <div class="d-flex align-items-center">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-info">
                <h4>Hello, {{ username }}!</h4>
                <p>You have successfully authenticated with Maximo</p>
            </div>
        </div>
    </div>

    <!-- Status Alert -->
    <div class="status-alert">
        <i class="fas fa-shield-check"></i>
        <span>Your OAuth authentication was successful. You now have an active session with Maximo.</span>
    </div>

    <!-- Performance Metrics Card -->
    <div class="metrics-card">
        <div class="metrics-header">
            <i class="fas fa-tachometer-alt"></i>
            <span>Login Performance Metrics</span>
        </div>
        <div class="metrics-body">
            <div class="metric-item">
                <span class="metric-label">Login Duration:</span>
                <span class="metric-value metric-success">{{ "%.2f"|format(login_duration) }}s</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">Authentication Method:</span>
                <span class="metric-value metric-primary">Lightning-Fast OAuth</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">Session Expires:</span>
                <span class="metric-value metric-info">{{ ((token_expires_at - time.time()) / 60)|int }} min</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">Optimizations:</span>
                <div>
                    <span class="metric-value metric-secondary me-1">Connection Pooling</span>
                    <span class="metric-value metric-secondary">Token Caching</span>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Insights Card -->
    <div class="ai-insights-card">
        <div class="ai-insights-header">
            <i class="fas fa-robot"></i>
            <span>AI Insights</span>
        </div>
        <div class="ai-insights-body" id="ai-insights-content">
            <div class="ai-insight-item">
                <i class="fas fa-lightbulb ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Welcome to Maximo Mobile!</strong> Your authentication is secure and optimized for performance.
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-chart-line ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Performance Tip:</strong> Your login completed in {{ "%.2f"|format(login_duration) }} seconds - that's {{ "excellent" if login_duration < 2 else "good" if login_duration < 5 else "acceptable" }}!
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-clipboard-list ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Next Steps:</strong> Access your work orders to view tasks, materials, and labor records with enhanced mobile functionality.
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-sync-alt ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Sync Recommendation:</strong> Consider syncing your data for offline capabilities. Start with Users & Profiles for basic functionality.
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-mobile-alt ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Mobile Optimized:</strong> This welcome page is now fully responsive with touch-friendly buttons, elegant design, and persistent AI insights.
                </div>
            </div>
        </div>
    </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('profile') }}" class="btn btn-primary me-2">
                <i class="fas fa-user me-2"></i>View Profile
            </a>
            <a href="{{ url_for('enhanced_profile') }}" class="btn btn-success me-2">
                <i class="fas fa-rocket me-2"></i>Enhanced Profile
            </a>
            <a href="{{ url_for('enhanced_workorders') }}" class="btn btn-primary me-2">
                <i class="fas fa-clipboard-check me-2"></i>Enhanced Work Orders
            </a>

            <a href="{{ url_for('sync') }}" class="btn btn-info me-2">
                <i class="fas fa-database me-2"></i>Sync Data
            </a>
            <a href="/api-docs/mxapiste" class="btn btn-success me-2">
                <i class="fas fa-building me-2"></i>Site API Docs
            </a>
            <a href="{{ url_for('logout') }}" class="btn btn-outline-primary">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </div>
</div>

<div class="card border-0 shadow-sm mt-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-cog me-2"></i>OAuth Details
        </h5>
    </div>
    <div class="card-body p-4">
        <p>This implementation uses OAuth to authenticate with Maximo. The authentication flow includes:</p>

        <ol>
            <li>Initiating the OAuth flow by accessing the Maximo URL</li>
            <li>Finding the authentication endpoint</li>
            <li>Submitting credentials to the login endpoint</li>
            <li>Extracting and storing tokens from the response</li>
            <li>Verifying the authentication by accessing a protected resource</li>
        </ol>

        <div class="mt-3">
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="themeSwitch">
                <label class="form-check-label" for="themeSwitch">Dark Mode</label>
            </div>
        </div>
    </div>
</div>
{% endblock %}

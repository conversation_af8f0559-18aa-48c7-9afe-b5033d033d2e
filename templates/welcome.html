{% extends 'base.html' %}

{% block title %}Welcome - <PERSON><PERSON>{% endblock %}

{% block extra_css %}
<style>
/* Welcome page specific responsive styles */
.welcome-container {
    max-width: 100%;
    padding: 0.5rem;
}

.welcome-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    text-align: center;
    box-shadow: var(--box-shadow);
}

.welcome-header h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.welcome-header .badge {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.user-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    border: none;
}

.user-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    margin-right: 1rem;
    flex-shrink: 0;
}

.user-info h4 {
    color: var(--text-color);
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.user-info p {
    color: var(--text-color);
    opacity: 0.7;
    margin-bottom: 0;
    font-size: 0.9rem;
}

.status-alert {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.status-alert i {
    font-size: 1.2rem;
    margin-right: 0.75rem;
}

/* Performance metrics card */
.metrics-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.metrics-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.metrics-header i {
    margin-right: 0.5rem;
}

.metrics-body {
    padding: 1rem;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 500;
    color: var(--text-color);
}

.metric-value {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-size: 0.85rem;
}

.metric-success {
    background-color: var(--success-color);
    color: white;
}

.metric-primary {
    background-color: var(--primary-color);
    color: white;
}

.metric-info {
    background-color: var(--secondary-color);
    color: white;
}

.metric-secondary {
    background-color: var(--light-color);
    color: var(--dark-color);
}

/* Action buttons */
.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.75rem;
    margin: 1rem 0;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.action-btn span {
    font-size: 0.85rem;
    text-align: center;
}

.btn-primary-action {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-success-action {
    background: linear-gradient(135deg, var(--success-color), #27ae60);
    color: white;
}

.btn-info-action {
    background: linear-gradient(135deg, var(--secondary-color), #3498db);
    color: white;
}

.btn-outline-action {
    background: var(--card-bg);
    color: var(--text-color);
    border: 2px solid var(--border-color);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    color: inherit;
}

/* AI Insights section */
.ai-insights-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
    border-left: 4px solid var(--secondary-color);
}

.ai-insights-header {
    background: linear-gradient(135deg, var(--secondary-color), #3498db);
    color: white;
    padding: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.ai-insights-header i {
    margin-right: 0.5rem;
    font-size: 1.1rem;
}

.ai-insights-body {
    padding: 1rem;
}

.ai-insight-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    padding: 0.75rem;
    background: rgba(var(--secondary-color-rgb), 0.05);
    border-radius: 6px;
    border-left: 3px solid var(--secondary-color);
}

.ai-insight-item:last-child {
    margin-bottom: 0;
}

.ai-insight-icon {
    color: var(--secondary-color);
    margin-right: 0.75rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.ai-insight-content {
    flex: 1;
    line-height: 1.4;
    color: var(--text-color);
}

.ai-insight-content strong {
    color: var(--primary-color);
}

/* OAuth details card */
.oauth-card {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
    overflow: hidden;
}

.oauth-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-color));
    color: white;
    padding: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.oauth-header i {
    margin-right: 0.5rem;
}

.oauth-body {
    padding: 1rem;
}

.oauth-steps {
    list-style: none;
    padding: 0;
    margin: 0;
}

.oauth-steps li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: rgba(var(--primary-color-rgb), 0.05);
    border-radius: 6px;
}

.oauth-steps li:before {
    content: counter(step-counter);
    counter-increment: step-counter;
    background: var(--primary-color);
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.oauth-steps {
    counter-reset: step-counter;
}

/* Theme toggle */
.theme-toggle-section {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.theme-toggle {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.theme-toggle label {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0;
}

.theme-toggle i {
    margin-right: 0.5rem;
    color: var(--secondary-color);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .welcome-container {
        padding: 0.25rem;
    }

    .welcome-header {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    .welcome-header h1 {
        font-size: 1.25rem;
    }

    .user-card {
        padding: 1rem;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .action-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .action-btn {
        padding: 0.75rem 0.5rem;
    }

    .action-btn i {
        font-size: 1.25rem;
    }

    .action-btn span {
        font-size: 0.8rem;
    }

    .metrics-body,
    .ai-insights-body,
    .oauth-body {
        padding: 0.75rem;
    }

    .metric-item {
        padding: 0.5rem 0;
    }

    .ai-insight-item {
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }
}

/* Touch-friendly improvements */
.action-btn, .btn-mobile-friendly {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    touch-action: manipulation;
}

/* Enhanced visual feedback */
.ai-insight-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.ai-insight-item:hover {
    background: rgba(var(--secondary-color-rgb), 0.1);
    transform: translateX(5px);
}

.ai-insight-item:active {
    transform: translateX(3px) scale(0.98);
}

/* Loading states */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Dark mode specific adjustments */
[data-bs-theme="dark"] .loading-shimmer {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
    background-size: 200% 100%;
}

/* Accessibility improvements */
.action-btn:focus,
.btn-mobile-friendly:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

.ai-insight-item:focus {
    outline: 1px solid var(--secondary-color);
    outline-offset: 1px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .welcome-header {
        border: 2px solid var(--text-color);
    }

    .action-btn {
        border: 2px solid currentColor;
    }

    .ai-insight-item {
        border: 1px solid var(--border-color);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .action-btn,
    .ai-insight-item,
    .metric-item,
    .loading-shimmer {
        transition: none;
        animation: none;
    }

    .action-btn:hover {
        transform: none;
    }
}

@media (max-width: 480px) {
    .action-buttons {
        grid-template-columns: 1fr;
    }

    .metric-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .metric-value {
        align-self: flex-end;
    }

    .welcome-header h1 {
        font-size: 1.1rem;
    }

    .user-avatar {
        width: 45px;
        height: 45px;
        font-size: 1.3rem;
    }

    .user-info h4 {
        font-size: 1rem;
    }

    .user-info p {
        font-size: 0.8rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="welcome-container">
    <!-- Welcome Header -->
    <div class="welcome-header">
        <h1>
            <i class="fas fa-check-circle me-2"></i>Authentication Successful
        </h1>
        <div class="badge">Test (UAT) Environment</div>
        <div class="badge bg-success mt-2">Mobile-Responsive v2.0</div>
    </div>

    <!-- User Information Card -->
    <div class="user-card">
        <div class="d-flex align-items-center">
            <div class="user-avatar">
                <i class="fas fa-user"></i>
            </div>
            <div class="user-info">
                <h4>Hello, {{ username }}!</h4>
                <p>You have successfully authenticated with Maximo</p>
            </div>
        </div>
    </div>

    <!-- Status Alert -->
    <div class="status-alert">
        <i class="fas fa-shield-check"></i>
        <span>Your OAuth authentication was successful. You now have an active session with Maximo.</span>
    </div>

    <!-- Performance Metrics Card -->
    <div class="metrics-card">
        <div class="metrics-header">
            <i class="fas fa-tachometer-alt"></i>
            <span>Login Performance Metrics</span>
        </div>
        <div class="metrics-body">
            <div class="metric-item">
                <span class="metric-label">Login Duration:</span>
                <span class="metric-value metric-success">{{ "%.2f"|format(login_duration) }}s</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">Authentication Method:</span>
                <span class="metric-value metric-primary">Lightning-Fast OAuth</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">Session Expires:</span>
                <span class="metric-value metric-info">{{ ((token_expires_at - time.time()) / 60)|int }} min</span>
            </div>
            <div class="metric-item">
                <span class="metric-label">Optimizations:</span>
                <div>
                    <span class="metric-value metric-secondary me-1">Connection Pooling</span>
                    <span class="metric-value metric-secondary">Token Caching</span>
                </div>
            </div>
        </div>
    </div>

    <!-- AI Insights Card -->
    <div class="ai-insights-card">
        <div class="ai-insights-header">
            <i class="fas fa-robot"></i>
            <span>AI Insights</span>
        </div>
        <div class="ai-insights-body" id="ai-insights-content">
            <div class="ai-insight-item">
                <i class="fas fa-lightbulb ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Welcome to Maximo Mobile!</strong> Your authentication is secure and optimized for performance.
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-chart-line ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Performance Tip:</strong> Your login completed in {{ "%.2f"|format(login_duration) }} seconds - that's {{ "excellent" if login_duration < 2 else "good" if login_duration < 5 else "acceptable" }}!
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-clipboard-list ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Next Steps:</strong> Access your work orders to view tasks, materials, and labor records with enhanced mobile functionality.
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-sync-alt ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Sync Recommendation:</strong> Consider syncing your data for offline capabilities. Start with Users & Profiles for basic functionality.
                </div>
            </div>
            <div class="ai-insight-item">
                <i class="fas fa-mobile-alt ai-insight-icon"></i>
                <div class="ai-insight-content">
                    <strong>Mobile Optimized:</strong> This welcome page is now fully responsive with touch-friendly buttons, elegant design, and persistent AI insights.
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
        <a href="{{ url_for('profile') }}" class="action-btn btn-primary-action">
            <i class="fas fa-user"></i>
            <span>View Profile</span>
        </a>
        <a href="/enhanced-workorders" class="action-btn btn-success-action">
            <i class="fas fa-clipboard-list"></i>
            <span>Work Orders</span>
        </a>
        <a href="/sync" class="action-btn btn-info-action">
            <i class="fas fa-sync-alt"></i>
            <span>Data Sync</span>
        </a>
        <a href="/api-docs" class="action-btn btn-info-action">
            <i class="fas fa-code"></i>
            <span>API Docs</span>
        </a>
        <a href="{{ url_for('logout') }}" class="action-btn btn-outline-action">
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
        </a>
        <button onclick="testAIInsights()" class="action-btn btn-info-action">
            <i class="fas fa-flask"></i>
            <span>Test AI</span>
        </button>
    </div>

    <!-- OAuth Details Card -->
    <div class="oauth-card">
        <div class="oauth-header">
            <i class="fas fa-cog"></i>
            <span>OAuth Authentication Details</span>
        </div>
        <div class="oauth-body">
            <p style="margin-bottom: 1rem; color: var(--text-color); opacity: 0.8;">This implementation uses OAuth to authenticate with Maximo. The authentication flow includes:</p>

            <ol class="oauth-steps">
                <li>Initiating the OAuth flow by accessing the Maximo URL</li>
                <li>Finding the authentication endpoint</li>
                <li>Submitting credentials to the login endpoint</li>
                <li>Extracting and storing tokens from the response</li>
                <li>Verifying the authentication by accessing a protected resource</li>
            </ol>

            <div class="theme-toggle-section">
                <div class="theme-toggle">
                    <label for="themeSwitch">
                        <i class="fas fa-moon"></i>
                        Dark Mode
                    </label>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="themeSwitch">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile-friendly bottom navigation (only visible on mobile) -->
    <div class="welcome-mobile-nav d-md-none">
        <div class="nav-grid">
            <a href="/enhanced-workorders" class="nav-item">
                <i class="fas fa-clipboard-list"></i>
                <span>Work Orders</span>
            </a>
            <a href="/sync" class="nav-item">
                <i class="fas fa-sync-alt"></i>
                <span>Sync Data</span>
            </a>
            <a href="{{ url_for('logout') }}" class="nav-item">
                <i class="fas fa-sign-out-alt"></i>
                <span>Logout</span>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// AI Insights persistence and management
class AIInsightsManager {
    constructor() {
        this.storageKey = 'maximo_ai_insights';
        this.insights = this.loadInsights();
        this.init();
    }

    init() {
        this.updateInsightsDisplay();
        this.startPeriodicUpdates();
        this.addSessionInsights();
    }

    loadInsights() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (e) {
            console.error('Error loading AI insights:', e);
            return [];
        }
    }

    saveInsights() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.insights));
        } catch (e) {
            console.error('Error saving AI insights:', e);
        }
    }

    addInsight(insight) {
        const timestamp = new Date().toISOString();
        const newInsight = {
            id: Date.now() + Math.random(),
            ...insight,
            timestamp: timestamp,
            persistent: insight.persistent || false
        };

        this.insights.unshift(newInsight);

        // Keep only last 10 insights, but preserve persistent ones
        const persistentInsights = this.insights.filter(i => i.persistent);
        const recentInsights = this.insights.filter(i => !i.persistent).slice(0, 10 - persistentInsights.length);
        this.insights = [...persistentInsights, ...recentInsights];

        this.saveInsights();
        this.updateInsightsDisplay();
    }

    addSessionInsights() {
        // Add session-specific insights
        const loginDuration = {{ login_duration }};
        const sessionMinutes = {{ ((token_expires_at - time.time()) / 60)|int }};

        // Performance insight
        if (loginDuration < 2) {
            this.addInsight({
                icon: 'fas fa-rocket',
                content: `<strong>Excellent Performance!</strong> Your login completed in ${loginDuration.toFixed(2)} seconds - faster than 90% of users.`,
                type: 'performance',
                persistent: false
            });
        }

        // Session insight
        if (sessionMinutes > 60) {
            this.addInsight({
                icon: 'fas fa-clock',
                content: `<strong>Extended Session:</strong> Your session is valid for ${sessionMinutes} minutes - perfect for long work sessions.`,
                type: 'session',
                persistent: false
            });
        }

        // Feature recommendations based on time of day
        const hour = new Date().getHours();
        if (hour >= 9 && hour <= 17) {
            this.addInsight({
                icon: 'fas fa-briefcase',
                content: '<strong>Work Hours Detected:</strong> Consider checking your assigned work orders and updating task progress.',
                type: 'recommendation',
                persistent: false
            });
        } else {
            this.addInsight({
                icon: 'fas fa-moon',
                content: '<strong>After Hours Access:</strong> Perfect time for planning and reviewing completed work orders.',
                type: 'recommendation',
                persistent: false
            });
        }
    }

    updateInsightsDisplay() {
        const container = document.getElementById('ai-insights-content');
        if (!container) return;

        // Clear existing dynamic insights (keep the static ones)
        const staticInsights = container.querySelectorAll('.ai-insight-item[data-static="true"]');
        container.innerHTML = '';

        // Re-add static insights
        staticInsights.forEach(insight => container.appendChild(insight));

        // Add dynamic insights
        this.insights.slice(0, 6).forEach(insight => {
            const insightElement = this.createInsightElement(insight);
            container.appendChild(insightElement);
        });
    }

    createInsightElement(insight) {
        const div = document.createElement('div');
        div.className = 'ai-insight-item';
        div.innerHTML = `
            <i class="${insight.icon} ai-insight-icon"></i>
            <div class="ai-insight-content">
                ${insight.content}
                <small style="display: block; margin-top: 0.25rem; opacity: 0.6;">
                    ${this.formatTimestamp(insight.timestamp)}
                </small>
            </div>
        `;
        return div;
    }

    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins} minutes ago`;
        if (diffMins < 1440) return `${Math.floor(diffMins / 60)} hours ago`;
        return date.toLocaleDateString();
    }

    startPeriodicUpdates() {
        // Update insights every 5 minutes
        setInterval(() => {
            this.checkForNewInsights();
        }, 5 * 60 * 1000);
    }

    checkForNewInsights() {
        // Check session status and add relevant insights
        fetch('/api/session-status')
            .then(response => response.json())
            .then(data => {
                if (data.warning && data.minutes_remaining < 10) {
                    this.addInsight({
                        icon: 'fas fa-exclamation-triangle',
                        content: `<strong>Session Warning:</strong> Your session expires in ${data.minutes_remaining} minutes. Consider refreshing your authentication.`,
                        type: 'warning',
                        persistent: false
                    });
                }
            })
            .catch(error => console.error('Error checking session status:', error));
    }
}

// Theme toggle functionality
function initThemeToggle() {
    const themeSwitch = document.getElementById('themeSwitch');
    const html = document.documentElement;

    // Load saved theme
    const savedTheme = localStorage.getItem('theme') || 'light';
    html.setAttribute('data-bs-theme', savedTheme);
    themeSwitch.checked = savedTheme === 'dark';

    // Theme switch handler
    themeSwitch.addEventListener('change', function() {
        const theme = this.checked ? 'dark' : 'light';
        html.setAttribute('data-bs-theme', theme);
        localStorage.setItem('theme', theme);

        // Add insight about theme change
        if (window.aiInsights) {
            window.aiInsights.addInsight({
                icon: this.checked ? 'fas fa-moon' : 'fas fa-sun',
                content: `<strong>Theme Changed:</strong> Switched to ${theme} mode for better ${this.checked ? 'low-light' : 'daylight'} viewing.`,
                type: 'preference',
                persistent: false
            });
        }
    });
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Mark static insights
    document.querySelectorAll('#ai-insights-content .ai-insight-item').forEach(item => {
        item.setAttribute('data-static', 'true');
    });

    // Initialize AI Insights Manager
    window.aiInsights = new AIInsightsManager();

    // Initialize theme toggle
    initThemeToggle();

    // Add welcome insight
    setTimeout(() => {
        window.aiInsights.addInsight({
            icon: 'fas fa-star',
            content: '<strong>Welcome Back!</strong> All systems are operational and ready for your work order management tasks.',
            type: 'welcome',
            persistent: false
        });
    }, 2000);
});

// Test AI insights functionality
function testAIInsights() {
    const testTypes = ['performance', 'recommendation', 'test'];
    const randomType = testTypes[Math.floor(Math.random() * testTypes.length)];

    fetch('/api/test-ai-insight', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ type: randomType })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && window.aiInsights) {
            window.aiInsights.addInsight(data.insight);
        } else {
            console.error('Failed to generate test insight:', data.error);
        }
    })
    .catch(error => {
        console.error('Error testing AI insights:', error);
    });
}

// Expose functions for external use
window.addAIInsight = function(insight) {
    if (window.aiInsights) {
        window.aiInsights.addInsight(insight);
    }
};

window.testAIInsights = testAIInsights;

// Mobile-specific enhancements
function initMobileEnhancements() {
    // Add touch feedback for mobile buttons
    if ('ontouchstart' in window) {
        document.querySelectorAll('.action-btn, .btn-mobile-friendly').forEach(btn => {
            btn.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.95)';
            });

            btn.addEventListener('touchend', function() {
                this.style.transform = '';
            });
        });
    }

    // Add swipe gesture for AI insights
    let startX = 0;
    let startY = 0;
    const aiInsightsCard = document.querySelector('.ai-insights-card');

    if (aiInsightsCard) {
        aiInsightsCard.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });

        aiInsightsCard.addEventListener('touchend', function(e) {
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            const diffX = startX - endX;
            const diffY = startY - endY;

            // Horizontal swipe
            if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
                if (diffX > 0) {
                    // Swipe left - refresh insights
                    if (window.aiInsights) {
                        window.aiInsights.addInsight({
                            icon: 'fas fa-refresh',
                            content: '<strong>Insights Refreshed:</strong> Swipe detected! Checking for new recommendations...',
                            type: 'interaction',
                            persistent: false
                        });
                    }
                }
            }
        });
    }
}

// Performance monitoring
function initPerformanceMonitoring() {
    // Monitor page load performance
    window.addEventListener('load', function() {
        const loadTime = performance.now();

        if (window.aiInsights && loadTime > 0) {
            const performanceLevel = loadTime < 1000 ? 'excellent' :
                                   loadTime < 2000 ? 'good' :
                                   loadTime < 3000 ? 'fair' : 'needs improvement';

            setTimeout(() => {
                window.aiInsights.addInsight({
                    icon: 'fas fa-stopwatch',
                    content: `<strong>Page Performance:</strong> Welcome page loaded in ${loadTime.toFixed(0)}ms - ${performanceLevel}!`,
                    type: 'performance',
                    persistent: false
                });
            }, 3000);
        }
    });

    // Monitor network status
    if ('navigator' in window && 'onLine' in navigator) {
        function updateNetworkStatus() {
            const isOnline = navigator.onLine;
            if (window.aiInsights) {
                window.aiInsights.addInsight({
                    icon: isOnline ? 'fas fa-wifi' : 'fas fa-wifi-slash',
                    content: `<strong>Network Status:</strong> You are currently ${isOnline ? 'online' : 'offline'}. ${isOnline ? 'All features available.' : 'Some features may be limited.'}`,
                    type: 'network',
                    persistent: false
                });
            }
        }

        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);
    }
}

// Enhanced initialization
document.addEventListener('DOMContentLoaded', function() {
    // Add a console log to verify our JavaScript is loading
    console.log('🎉 Mobile-Responsive Welcome Page v2.0 Loaded!');
    console.log('📱 AI Insights System Active');

    // Add a visual indicator that our changes are active
    const header = document.querySelector('.welcome-header h1');
    if (header) {
        header.style.textShadow = '0 2px 4px rgba(0,0,0,0.3)';
        console.log('✅ Header styling applied');
    }
    // Mark static insights
    document.querySelectorAll('#ai-insights-content .ai-insight-item').forEach(item => {
        item.setAttribute('data-static', 'true');
    });

    // Initialize AI Insights Manager
    window.aiInsights = new AIInsightsManager();

    // Initialize theme toggle
    initThemeToggle();

    // Initialize mobile enhancements
    initMobileEnhancements();

    // Initialize performance monitoring
    initPerformanceMonitoring();

    // Add welcome insight
    setTimeout(() => {
        window.aiInsights.addInsight({
            icon: 'fas fa-star',
            content: '<strong>Welcome Back!</strong> All systems are operational and ready for your work order management tasks.',
            type: 'welcome',
            persistent: false
        });
    }, 2000);

    // Add device-specific insights
    const isMobile = window.innerWidth <= 768;
    if (isMobile) {
        setTimeout(() => {
            window.aiInsights.addInsight({
                icon: 'fas fa-mobile-alt',
                content: '<strong>Mobile Optimized:</strong> This interface is optimized for mobile use. Swipe left on insights to refresh!',
                type: 'mobile',
                persistent: false
            });
        }, 4000);
    }
});
</script>
{% endblock %}
